import {formatMinuteToStr, getTimeRange, getWeekRange, isOverTimeFun} from "./time";
import {validateNecessary} from "./validate";
import { getGraphNodeName, replaceBase64Text, setRightTipAttr } from "./antvGraph"
import {
  buildComponent,
  cloneDeep,
  dataURLtoFile,
  diGuiTreeEdit,
  downloadData,
  downloadUrl,
  equals,
  fileTranslate,
  getDiffItemInAry,
  intervalAnimate,
  isSameArray,
  numAbbrev,
  numByThree,
  randomNum,
} from "./transfrom";
import { formatValue } from "../libs/util";

export {
  getTimeRange,
  getWeekRange,
  isOverTimeFun,
  formatMinuteToStr,
  fileTranslate,
  cloneDeep,
  downloadData,
  downloadUrl,
  intervalAnimate,
  dataURLtoFile,
  equals,
  isSameArray,
  getDiffItemInAry,
  buildComponent,
  numByThree,
  numAbbrev,
  diGuiTreeEdit,
  randomNum,
  validateNecessary,
  getGraphNodeName,
  replaceBase64Text,
  setRightTipAttr,
  formatValue,
}
