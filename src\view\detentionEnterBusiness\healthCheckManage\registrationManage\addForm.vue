<template>
  <div class="" style="height: 95%;overflow: hidden;">
    <div class="fm-content-info bsp-base-content" style="top:30px !important;">
      <Form ref="formData" :model="formData" :rules="ruleValidate" :label-width="200" label-colon
            style="padding: 0 .625rem;">
        <div class="fm-content-box">
          <p class="fm-content-info-title">
            <Icon type="md-list-box" size="24" color="#2b5fda"/>
            基本信息
          </p>
          <Row>
            <Col span="3" class="col-title"><span>姓名</span></Col>
            <Col span="5"><span>{{ formatValue(detentionEnterData.xm) }}</span></Col>
            <Col span="3" class="col-title"><span>性别</span></Col>
            <Col span="5"><span>{{ formatValue(detentionEnterData.xbName) }}</span></Col>
            <Col span="3" class="col-title"><span>年龄</span></Col>
            <Col span="5"><span>{{ formatValue(rowData.age) }}</span></Col>
          </Row>
          <Row>
            <Col span="3" class="col-title"><span>国籍</span></Col>
            <Col span="5"><span>{{ formatValue(detentionEnterData.gjName) }}</span></Col>
            <Col span="3" class="col-title"><span>监室号</span></Col>
            <Col span="5"><span>{{ formatValue(detentionEnterData.roomName) }}</span></Col>
            <Col span="3" class="col-title"><span>收押登记时间</span></Col>
            <Col span="5"><span>{{ formatValue(rowData.add_time) }}</span></Col>
          </Row>
        </div>

        <div class="bsp-base-subtit" style="height: 100%;overflow-x: hidden;overflow-y: auto;">
          <p class="fm-content-info-title">
            <Icon type="md-list-box" size="24" color="#2b5fda"/>
            健康检查登记
          </p>
          <div class="form">
            <Row>
              <Col span="8">
                <FormItem label="身高" prop="sg" :rules="[{ trigger: 'blur', message: '身高为必填', required: true }]">
                  <Input type="text" v-model="formData.sg" placeholder="请输入"/>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="体重" prop="tz" :rules="[{ trigger: 'blur', message: '体重为必填', required: true }]">
                  <Input type="text" v-model="formData.tz" placeholder="请输入"/>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="足长" prop="zc" :rules="[{ trigger: 'blur', message: '足长为必填', required: true }]">
                  <Input type="text" v-model="formData.zc" placeholder="请输入"/>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="8">
                <FormItem label="体温" prop="tw" :rules="[{ trigger: 'blur', message: '体温为必填', required: true }]">
                  <Input type="text" v-model="formData.tw" placeholder="请输入"/>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="血型" prop="xx">
                  <s-dicgrid v-model="formData.xx" @change="$refs.formData.validateField('xx')" :isSearch="true"
                             dicName="ZD_SYYWXX"/>
                </FormItem>
                <!--                <FormItem label="血型" prop="xx" :rules="[{ trigger: 'blur', message: '血型为必填', required: true }]">-->
                <!--                  <Input type="text" v-model="formData.xx" placeholder="请输入"/>-->
                <!--                </FormItem>-->
              </Col>
              <Col span="8">
                <FormItem label="血压" prop="xyszy"
                          :rules="[{ trigger: 'blur', message: '血压为必填', required: true }]">
                  <Input type="text" v-model="formData.xyszy" placeholder="请输入"/>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="8">
                <FormItem label="女性妊娠检查结果" prop="nxrzjcg">
                  <s-dicgrid v-model="formData.nxrzjcg" @change="$refs.formData.validateField('nxrzjcg')"
                             :isSearch="true" dicName="ZD_JYYWTYYW"/>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="心率" prop="xl" :rules="[{ trigger: 'blur', message: '心率为必填', required: true }]">
                  <Input type="text" v-model="formData.xl" placeholder="请输入"/>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="健康状况" prop="jkzk"
                          :rules="[{ trigger: 'blur,change', message: '健康状况为必填', required: true }]">
                  <s-dicgrid v-model="formData.jkzk" @change="$refs.formData.validateField('jkzk')" :isSearch="true"
                             dicName="ZD_SYYWJKZK"/>
                </FormItem>

                <!--                <FormItem label="健康状况" prop="jkzk" :rules="[{ trigger: 'blur', message: '健康状况为必填', required: true }]">-->
                <!--                  <Input type="text" v-model="formData.jkzk" placeholder="请输入"/>-->
                <!--                </FormItem>-->
              </Col>

            </Row>
            <Row>
              <Col span="8">
                <!--                <FormItem label="语言表达能力是否正常" prop="yybdnl" :rules="[{ trigger: 'blur,change', message: '请选择语言表达能力是否正常', required: true }]">-->
                <!--                  <RadioGroup v-model="formData.yybdnl">-->
                <!--                    <Radio label="1">是</Radio>-->
                <!--                    <Radio label="2">否</Radio>-->
                <!--                  </RadioGroup>-->
                <!--                </FormItem>-->
                <FormItem label="语言表达具体情况" prop="yybdnljtqk"
                          :rules="[{ trigger: 'blur,change', message: '语言表达具体情况为必填', required: true }]">
                  <s-dicgrid v-model="formData.yybdnljtqk" @values="yybdnlChange"
                             @change="$refs.formData.validateField('yybdnljtqk')" :isSearch="true"
                             dicName="ZD_SYYWYYBDNLJTQK"/>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="肢体活动具体情况" prop="zthdjtqk"
                          :rules="[{ trigger: 'blur,change', message: '肢体活动具体情况为必填', required: true }]">
                  <s-dicgrid v-model="formData.zthdjtqk" @values="zthdjtqkChange"
                             @change="$refs.formData.validateField('zthdjtqk')" :isSearch="true"
                             dicName="ZD_SYYWZTHDJTQK"/>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="视力具体情况" prop="sljtqk"
                          :rules="[{ trigger: 'blur,change', message: '视力具体情况为必填', required: true }]">
                  <s-dicgrid v-model="formData.sljtqk" @values="sljtqkChange"
                             @change="$refs.formData.validateField('sljtqk')" :isSearch="true" dicName="ZD_SYYWSLJTQK"/>
                </FormItem>
              </Col>

            </Row>

            <Row>
              <Col span="8">
                <FormItem label="听力具体情况" prop="tlzkjtqk"
                          :rules="[{ trigger: 'blur,change', message: '听力具体情况为必填', required: true }]">
                  <s-dicgrid v-model="formData.tlzkjtqk" @values="tlzkjtqkChange"
                             @change="$refs.formData.validateField('tlzkjtqk')" :isSearch="true"
                             dicName="ZD_SYYWTLJTQK"/>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="智力具体情况" prop="zlzkjtqk"
                          :rules="[{ trigger: 'blur,change', message: '智力具体情况为必填', required: true }]">
                  <s-dicgrid v-model="formData.zlzkjtqk" @values="zlzkjtqkChange"
                             @change="$refs.formData.validateField('zlzkjtqk')" :isSearch="true"
                             dicName="ZD_SYYWZLJTQK"/>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="精神具体情况" prop="jszkjtqk"
                          :rules="[{ trigger: 'blur,change', message: '精神具体情况为必填', required: true }]">
                  <s-dicgrid v-model="formData.jszkjtqk" @values="jszkjtqkChange"
                             @change="$refs.formData.validateField('jszkjtqk')" :isSearch="true"
                             dicName="ZD_SYYWJSJTQK"/>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="8">
                <FormItem label="有无吸毒史" prop="ywdxs">
                  <s-dicgrid v-model="formData.ywdxs" @change="$refs.formData.validateField('ywdxs')" :isSearch="true"
                             dicName="ZD_JYYWTYYW"/>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="严重传染病情况" prop="yzcbrbmc"
                          :rules="[{ trigger: 'blur,change', message: '严重传染病情况为必填', required: true }]">
                  <s-dicgrid v-model="formData.yzcbrbmc" @values="yzcbrbmcChange"
                             @change="$refs.formData.validateField('yzcbrbmc')" :isSearch="true"
                             dicName="ZD_SYYWYZCRBMC"/>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="既往病史情况" prop="jwjblx"
                          :rules="[{ trigger: 'blur,change', message: '既往病史情况为必填', required: true }]">
                  <s-dicgrid v-model="formData.jwjblx" @values="jwjblxChange"
                             @change="$refs.formData.validateField('jwjblx')" :isSearch="true" dicName="ZD_SYYWJWJBLX"/>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="8">
                <FormItem label="B超" prop="bcscdzUrl" style="width: 100%;">
                  <file-upload
                    :defaultList="bcscdzUrl"
                    :serviceMark="serviceMark"
                    :bucketName="bucketName"
                    :beforeUpload="beforeUpload"
                    v-if="bcscdzShowFile"
                    @fileSuccess="fileSuccessFile"
                    @fileRemove="fileRemoveFile"
                    @fileComplete="bcscdzFileCompleteFile"/>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="胸片" prop="xpscdzUrl" style="width: 100%;">
                  <file-upload
                    :defaultList="xpscdzUrl"
                    :serviceMark="serviceMark"
                    :bucketName="bucketName"
                    :beforeUpload="beforeUpload"
                    v-if="xpscdzShowFile"
                    @fileSuccess="fileSuccessFile"
                    @fileRemove="fileRemoveFile"
                    @fileComplete="xpscdzFileCompleteFile"/>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="胸部CT" prop="xbctscdzUrl" style="width: 100%;">
                  <file-upload
                    :defaultList="xbctscdzUrl"
                    :serviceMark="serviceMark"
                    :bucketName="bucketName"
                    :beforeUpload="beforeUpload"
                    v-if="xbctscdzShowFile"
                    @fileSuccess="fileSuccessFile"
                    @fileRemove="fileRemoveFile"
                    @fileComplete="xbctscdzFileCompleteFile"/>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="8">
                <FormItem label="血常规" prop="xcgscdzUrl" style="width: 100%;">
                  <file-upload
                    :defaultList="xcgscdzUrl"
                    :serviceMark="serviceMark"
                    :bucketName="bucketName"
                    :beforeUpload="beforeUpload"
                    v-if="xcgscdzShowFile"
                    @fileSuccess="fileSuccessFile"
                    @fileRemove="fileRemoveFile"
                    @fileComplete="xcgscdzFileCompleteFile"/>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="心电图" prop="xdtscdzUrl" style="width: 100%;">
                  <file-upload
                    :defaultList="xdtscdzUrl"
                    :serviceMark="serviceMark"
                    :bucketName="bucketName"
                    :beforeUpload="beforeUpload"
                    v-if="xdtscdzShowFile"
                    @fileSuccess="fileSuccessFile"
                    @fileRemove="fileRemoveFile"
                    @fileComplete="xdtscdzFileCompleteFile"/>
                </FormItem>
              </Col>
              <Col span="8">

              </Col>

            </Row>
          </div>

          <p class="fm-content-info-title">
            <Icon type="md-list-box" size="24" color="#2b5fda"/>
            伤情登记
          </p>
          <div class="form">
            <Row>
              <Col span="8">
                <FormItem label="外伤情况" prop="zswb"
                          :rules="[{ trigger: 'blur,change', message: '外伤情况为必填', required: true }]">
                  <s-dicgrid v-model="formData.zswb" @values="zswbChange" @change="$refs.formData.validateField('zswb')"
                             :isSearch="true" dicName="ZD_SQDJ"/>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="外伤情况记录方式" prop="wsqkjlfs">
                  <s-dicgrid v-model="formData.wsqkjlfs" @change="$refs.formData.validateField('wsqkjlfs')"
                             :isSearch="true" dicName="ZD_SYYWWSQKJLFS"/>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="外伤情况" prop="wsqkscdzUrl" style="width: 100%;">
                  <file-upload
                    :defaultList="wsqkscdzUrl"
                    :serviceMark="serviceMark"
                    :bucketName="bucketName"
                    :beforeUpload="beforeUpload"
                    v-if="wsqkscdzShowFile"
                    @fileSuccess="fileSuccessFile"
                    @fileRemove="fileRemoveFile"
                    @fileComplete="wsqkscdzFileCompleteFile"/>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="8">
                <FormItem label="致伤原因" prop="zsyy">
                  <Input type="text" v-model="formData.zsyy" placeholder="请输入"/>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="致伤日期" prop="zsrq">
                  <el-date-picker type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                  @change="$refs.formData.validateField('zsrq')" v-model="formData.zsrq" size="small"
                                  placeholder="请选择" style="width: 100%;"/>
                </FormItem>
              </Col>
              <Col span="8">

              </Col>
            </Row>
            <Row>
              <Col span="9">
                <div style="margin-left: 15px;">
                  <div class="man-model-box" @click="addLocation">
                    <div
                      v-if="dataTable && dataTable.length"
                      v-for="(data, index) in dataTable"
                      :key="index"
                      class="marker"
                      :style="{ left: data.zb.x + 'px', top: data.zb.y + 'px' }"
                    >
                      {{ index + 1 }}
                    </div>
                  </div>
                </div>

              </Col>
              <Col span="15">
                <Table :columns="columns" :data="dataTable" border class="bodyTable">
                  <template slot-scope="{ row, index }" slot="picture">
                    <div v-if="!row.zp" @click="takePhoto(row, index)"
                         style="width: 80px; height: 80px;border:1px dashed #dcdee2;border-radius: 6px;text-align: center;line-height: 100px;">
                      <Icon type="ios-add" size="60" color="rgb(187 187 187)"/>
                    </div>
                    <div v-if="row.zp">
                      <el-image
                        style="width: 80px; height: 80px"
                        :src="row.zp"
                        :zoom-rate="1.2"
                        :max-scale="7"
                        :min-scale="0.2"
                        :preview-src-list="row.zpList"
                        fit="cover"
                      />
                    </div>
                  </template>
                  <template slot-scope="{ row, index }" slot="ms">
                    <Input type="text" v-model="row.ms" @on-change="onDescriptionChange(row,index)"/>
                  </template>
                  <template slot-scope="{ row, index }" slot="action">
                    <Button type="error" size="small" @click="remove(index)">删除</Button>
                  </template>
                </Table>
              </Col>
            </Row>
          </div>

          <p class="fm-content-info-title">
            <Icon type="md-list-box" size="24" color="#2b5fda"/>
            健康检查结论
          </p>
          <div class="form">
            <Row>
              <Col span="8">
                <FormItem label="医生意见" prop="ysyj"
                          :rules="[{ trigger: 'blur,change', message: '医生意见为必填', required: true }]">
                  <s-dicgrid v-model="formData.ysyj" @change="$refs.formData.validateField('ysyj')" :isSearch="true"
                             dicName="ZD_SYRSYSYJ"/>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="检查人" prop="jcr"
                          :rules="[{ trigger: 'blur', message: '检查人为必填', required: true }]">
                  <Input type="text" v-model="formData.jcr" placeholder="请输入"/>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="检查时间" prop="jcsj"
                          :rules="[{ trigger: 'blur,change', message: '检查时间为必填', required: true }]">
                  <el-date-picker type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                                  @change="$refs.formData.validateField('jcsj')" v-model="formData.jcsj" size="small"
                                  placeholder="请选择" style="width: 100%;"/>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="16">
                <FormItem label="备注" prop="bz">
                  <Input v-model="formData.bz" placeholder="请填写" type="textarea"
                         :autosize="{minRows: 2,maxRows: 5}"></Input>
                </FormItem>
              </Col>
            </Row>
          </div>

        </div>
      </Form>
    </div>
    <div class="bsp-base-fotter">
      <Button style="margin: 0 20px" @click="handleClose()">返 回</Button>
      <Button style="margin: 0 20px" :loading="loadingSave" @click="handleSubmit(false)">暂 存</Button>
      <Button style="margin: 0 20px" type="primary" :loading="loading" @click="handleSubmit(true)">提交</Button>
    </div>
    <camera-modal ref="cameraModal" @takePhoto="takePhotoItem"></camera-modal>
  </div>
</template>

<script>
import {sImageUploadLocal} from '@/components/upload/image'
import {fileUpload} from 'sd-minio-upfile'
import {getUserCache} from '@/libs/util'
import {mapActions} from "vuex";
import {imgUpload} from 'sd-minio-upimg'
import cameraModal from '@/components/camera/camera-modal.vue'
import {formatValue} from '@/util'

export default {
  name: 'HealthCheckForm',
  components: {
    sImageUploadLocal, fileUpload, imgUpload, cameraModal
  },
  props: {
    rowData: {
      type: [Array, Object],
      default: {}
    },
    entireProcess: {
      default: false,
    },
    close: {
      default: false,
      type: Boolean
    }
  },
  data() {
    return {
      formData: {
        yybdnl: "02",
        yybdnljtqk: "01",
        zthdsfzc: "02",
        zthdjtqk: "01",
        slzksfzc: "02",
        sljtqk: "01",
        tlzksfzc: "02",
        tlzkjtqk: "01",
        zlzksfzc: "02",
        zlzkjtqk: "01",
        jszksfzc: "02",
        jszkjtqk: "01",
        ywyzcbrb: "01",
        yzcbrbmc: "01",
        jwbs: "01",
        jwjblx: "01",
        tbsfywss: "02",
        zswb: "01",
      },
      detentionEnterData: {},
      loading: false,
      loadingSave: false,
      ruleValidate: {},
      xcgscdzUrl: [],
      xcgscdzShowFile: false,
      xdtscdzUrl: [],
      xdtscdzShowFile: false,
      bcscdzUrl: [],
      bcscdzShowFile: false,
      xpscdzUrl: [],
      xpscdzShowFile: false,
      xbctscdzUrl: [],
      xbctscdzShowFile: false,
      wsqkscdzUrl: [],
      wsqkscdzShowFile: false,
      serviceMark: serverConfig.OSS_SERVICE_MARK,
      bucketName: serverConfig.bucketName,
      currentDataTableIndex: 0,
      columns: [
        {
          type: 'index',
          width: 80,
          align: 'center',
          title: '序号'
        },
        {
          title: '图片',
          slot: 'picture',
          width: 150,
          align: 'center',
        },
        {
          title: '描述',
          slot: 'ms',
          align: 'center',
        },
        {
          title: '操作',
          slot: 'action',
          width: 100,
          align: 'center'
        }
      ],
      dataTable: [],
      isTest: true,  //演示用
    }
  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),

    // 统一处理空值显示
    formatValue,

    getCurrentTimeFormatted() {
      const now = new Date();

      const year = now.getFullYear();             // 获取年份
      const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，要加1，并补零
      const day = String(now.getDate()).padStart(2, '0');        // 日期补零

      const hours = String(now.getHours()).padStart(2, '0');     // 小时补零
      const minutes = String(now.getMinutes()).padStart(2, '0'); // 分钟补零
      const seconds = String(now.getSeconds()).padStart(2, '0'); // 秒数补零

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    beforeUpload() {
    },
    fileSuccessFile() {
    },
    fileRemoveFile() {
    },
    xcgscdzFileCompleteFile(data, index) {
      console.log(data, "--------")
      if (data && data.length > 0) {
        this.$set(this.formData, 'xcgscdz', JSON.stringify(data))
      }
    },
    xdtscdzFileCompleteFile(data, index) {
      if (data && data.length > 0) {
        this.$set(this.formData, 'xdtscdz', JSON.stringify(data))
      }
    },
    bcscdzFileCompleteFile(data, index) {
      if (data && data.length > 0) {
        this.$set(this.formData, 'bcscdz', JSON.stringify(data))
      }
    },
    xpscdzFileCompleteFile(data, index) {
      if (data && data.length > 0) {
        this.$set(this.formData, 'xpscdz', JSON.stringify(data))
      }
    },
    xbctscdzFileCompleteFile(data, index) {
      if (data && data.length > 0) {
        this.$set(this.formData, 'xbctscdz', JSON.stringify(data))
      }
    },
    wsqkscdzFileCompleteFile(data, index) {
      if (data && data.length > 0) {
        this.$set(this.formData, 'wsqkscdz', JSON.stringify(data))
      }
    },
    onDescriptionChange(row, index) {
      console.log(this.dataTable, "this.dataTable-----")
      console.log(this.formData, "this.formData-----")
      this.dataTable[index].ms = row.ms
    },
    yybdnlChange(data) {
      if (data == "01") {
        this.formData.yybdnl = "01"
      } else {
        this.formData.yybdnl = "02"
        // this.formData.yybdnljtqk = data
      }
    },
    zthdjtqkChange(data) {
      if (data == "01") {
        this.formData.zthdsfzc = "01"
      } else {
        this.formData.zthdsfzc = "02"
        // this.formData.zthdjtqk = data
      }
    },
    sljtqkChange(data) {
      if (data == "01") {
        this.formData.slzksfzc = "01"
      } else {
        this.formData.slzksfzc = "02"
        // this.formData.sljtqk = data
      }
    },
    tlzkjtqkChange(data) {
      if (data == "01") {
        this.formData.tlzksfzc = "01"
      } else {
        this.formData.tlzksfzc = "02"
        // this.formData.tlzkjtqk = data
      }
    },
    zlzkjtqkChange(data) {
      if (data == "01") {
        this.formData.zlzksfzc = "01"
      } else {
        this.formData.zlzksfzc = "02"
        // this.formData.zlzkjtqk = data
      }
    },
    jszkjtqkChange(data) {
      if (data == "01") {
        this.formData.jszksfzc = "01"
      } else {
        this.formData.jszksfzc = "02"
        // this.formData.jszkjtqk = data
      }
    },
    yzcbrbmcChange(data) {
      if (data == "01") {
        this.formData.ywyzcbrb = "02"
      } else {
        this.formData.ywyzcbrb = "01"
        // this.formData.yzcbrbmc = data
      }
    },
    jwjblxChange(data) {
      if (data == "01") {
        this.formData.jwbs = "02"
      } else {
        this.formData.jwbs = "01"
        // this.formData.jwjblx = data
      }
    },
    zswbChange(data) {
      if (data == "01") {
        this.formData.tbsfywss = "02"
      } else {
        this.formData.tbsfywss = "01"
        // this.formData.zswb = data
      }
    },
    addLocation(event) {
      let target = event.currentTarget;
      let rect = target.getBoundingClientRect();
      // 计算相对于 div 左上角的坐标
      let x = event.clientX - rect.left;
      let y = event.clientY - rect.top;
      let data = {
        zb: {
          x: x,
          y: y
        },
        ms: "",
        zp: "",
      }
      this.dataTable.push(data)
    },
    takePhoto(row, index) {
      this.currentDataTableIndex = index
      this.$refs.cameraModal.open({});
    },
    takePhotoItem(imgUrl) {
      this.dataTable[this.currentDataTableIndex].zp = imgUrl
      if (!this.dataTable[this.currentDataTableIndex].zpList) {
        this.dataTable[this.currentDataTableIndex].zpList = []
      }
      this.dataTable[this.currentDataTableIndex].zpList.push(imgUrl)
    },
    remove(index) {
      if (this.dataTable.length > 0) {
        this.dataTable.splice(index, 1);
      }
    },
    handleClose() {
      this.$emit('close', false)
    },
    handleNext() {
      this.$emit('nextStep', false)
    },
    getDetail(rybh) {
      this.$store.dispatch('authGetRequest', {
        url: this.$path.app_healthCheckGetByRybh,
        params: {
          rybh: rybh
        }
      }).then(resp => {
        if (resp.code == 0) {
          if (!resp.data.status) {
            this.xcgscdzShowFile = true
            this.xdtscdzShowFile = true
            this.bcscdzShowFile = true
            this.xpscdzShowFile = true
            this.xbctscdzShowFile = true
            this.wsqkscdzShowFile = true
            return
          }
          this.formData = resp.data
          if (!this.formData.jcsj) {
            this.formData.jcsj = this.getCurrentTimeFormatted();
          }
          if (!this.formData.jcr) {
            this.$set(this.formData, 'jcr', getUserCache.getUserName())
          }
          this.formData.sg = this.formData.sg ? this.formData.sg.toString() : ""
          this.formData.tz = this.formData.tz ? this.formData.tz.toString() : ""
          this.formData.zc = this.formData.zc ? this.formData.zc.toString() : ""
          this.formData.tw = this.formData.tw ? this.formData.tw.toString() : ""
          this.formData.xyssy = this.formData.xyssy ? this.formData.xyssy.toString() : ""
          this.formData.xyszy = this.formData.xyszy ? this.formData.xyszy.toString() : ""
          this.formData.xl = this.formData.xl ? this.formData.xl.toString() : ""
          this.xcgscdzShowFile = true
          this.xdtscdzShowFile = true
          this.bcscdzShowFile = true
          this.xpscdzShowFile = true
          this.xbctscdzShowFile = true
          this.wsqkscdzShowFile = true
          if (this.formData.xcgscdz) {
            // this.formData.xcgscdz = JSON.parse(this.formData.xcgscdz)
            this.xcgscdzUrl = JSON.parse(this.formData.xcgscdz)
          }
          if (this.formData.xdtscdz) {
            // this.formData.xdtscdz = JSON.parse(this.formData.xdtscdz)
            this.xdtscdzUrl = JSON.parse(this.formData.xdtscdz)
          }
          if (this.formData.bcscdz) {
            // this.formData.bcscdz = JSON.parse(this.formData.bcscdz)
            this.bcscdzUrl = JSON.parse(this.formData.bcscdz)
          }
          if (this.formData.xpscdz) {
            // this.formData.xpscdz = JSON.parse(this.formData.xpscdz)
            this.xpscdzUrl = JSON.parse(this.formData.xpscdz)
          }
          if (this.formData.xbctscdz) {
            // this.formData.xbctscdz = JSON.parse(this.formData.xbctscdz)
            this.xbctscdzUrl = JSON.parse(this.formData.xbctscdz)
          }
          if (this.formData.wsqkscdz) {
            // this.formData.wsqkscdz = JSON.parse(this.formData.wsqkscdz)
            this.wsqkscdzUrl = JSON.parse(this.formData.wsqkscdz)
          }
          this.dataTable = []
          if (this.formData.injuryAssessmentList && this.formData.injuryAssessmentList.length > 0) {
            for (let i = 0; i < this.formData.injuryAssessmentList.length; i++) {
              let injuryAssessment = this.formData.injuryAssessmentList[i];
              let zpList = []
              zpList.push(injuryAssessment.zp)
              let info = {
                serialNumber: i + 1,
                id: injuryAssessment.id,
                ms: injuryAssessment.ms,
                zb: injuryAssessment.zb ? JSON.parse(injuryAssessment.zb) : "",
                zp: injuryAssessment.zp,
                zpList: zpList,
              }
              this.dataTable.push(info)
            }
          }
        } else {
          this.xcgscdzShowFile = true
          this.xdtscdzShowFile = true
          this.bcscdzShowFile = true
          this.xpscdzShowFile = true
          this.xbctscdzShowFile = true
          this.wsqkscdzShowFile = true
          this.$Modal.error({
            title: '温馨提示',
            content: resp.msg || '操作失败'
          })
        }
      })
    },
    handleSubmit(tag) {
      this.$refs['formData'].validate((valid) => {
        if (valid) {
          if (tag) {
            this.saveData(tag)
          } else {
            this.saveData()
          }
        } else {
          if (tag) {
            this.loading = false
          } else {
            this.loadingSave = false
          }
          this.$Message.error('请填写完整!!');
        }
      })
    },
    saveData(tag) {

      this.formData.dataSources ? '' : this.$set(this.formData, 'dataSources', 0)
      this.formData.name = this.detentionEnterData.xm;
      this.formData.rybh = this.rowData.rybh;
      let params = this.formData
      if (this.dataTable && this.dataTable.length) {
        let injuryAssessmentList = []
        for (let i = 0; i < this.dataTable.length; i++) {
          let injuryAssessment = {

            serialNumber: i + 1,
            ms: this.dataTable[i].ms,
            rybh: this.rowData.rybh,
            zb: this.dataTable[i].zb ? JSON.stringify(this.dataTable[i].zb) : "",
            zp: this.dataTable[i].zp
          }
          injuryAssessmentList.push(injuryAssessment)
        }
        params.injuryAssessmentList = injuryAssessmentList
      }
      if (tag) {
        this.loading = true
        params.status = "03"
      } else {
        this.loadingSave = true
        params.status = "02"
      }
      params.businessType = "kss"
      params.rslx = this.rowData.rslx
      let url = this.$path.app_healthCheckCreate;
      if (this.rowData.jkjcStatus == "02") {
        url = this.$path.app_healthCheckUpdate;
      }
      this.$store.dispatch('authPostRequest', {
        url: url,
        params: params
      }).then(resp => {
        this.loading = false
        this.loadingSave = false
        if (resp.code == 0) {
          if (tag) {
            this.$Message.success('提交成功!');
          } else {
            this.$Message.success('保存成功!');
          }
          if (this.close) {
            this.handleClose();
            return
          }
          if (tag && this.isTest) {
            this.handleNext();
          } else {
            this.handleClose();
          }
        } else {
          this.$Notice.error({
            title: '错误提示',
            desc: resp.msg
          })
        }
      })
    },
    getDetentionEnterDetail(rybh) {
      this.$store.dispatch('authPostRequest', {
        url: this.$path.app_detainRegKssGetList,
        params: {
          rybh: rybh
        }
      }).then(resp => {
        if (resp.code == 0) {
          // this.formData = resp.data
          if (resp.data && resp.data.length > 0) {
            this.detentionEnterData = resp.data[0]
            this.formData.name = this.detentionEnterData.xm;
          }
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: resp.msg || '操作失败'
          })
        }
      })
    },
  },
  mounted() {
    this.formData.jcsj = this.getCurrentTimeFormatted();
    this.$set(this.formData, 'jcr', getUserCache.getUserName())
    if (this.entireProcess) {
      if (this.rowData.rybh) {
        this.getDetentionEnterDetail(this.rowData.rybh)
        this.getDetail(this.rowData.rybh)
      } else {
        this.xcgscdzShowFile = true
        this.xdtscdzShowFile = true
        this.bcscdzShowFile = true
        this.xpscdzShowFile = true
        this.xbctscdzShowFile = true
        this.wsqkscdzShowFile = true
      }
    } else {
      this.formData = JSON.parse(JSON.stringify(this.rowData))
      this.formData.name = this.formData.xm;
      if (this.rowData.status == "02") {
        this.getDetail(this.rowData.rybh)
      } else {
        this.xcgscdzShowFile = true
        this.xdtscdzShowFile = true
        this.bcscdzShowFile = true
        this.xpscdzShowFile = true
        this.xbctscdzShowFile = true
        this.wsqkscdzShowFile = true
      }
    }
  }
}
</script>

<style scoped lang="less">
@import "~@/assets/style/formInfo.css";

.bsp-imgminio-container {
  width: 100% !important;
}

.man-model-box {
  position: relative;
  width: 401px;
  height: 334px;
  border: 1px solid #E5E5E5;
  background: url("~@/assets/images/detentionEnter/fullBodyPhoto.png");
  background-size: contain; /* 或者 'cover' 根据需求选择 */
  background-position: center;
  background-repeat: no-repeat;
  /*margin-left: 110px;*/
}

.bodyTable {
  ::v-deep(.input-container) {
    width: 80px !important;
    height: 80px !important;
    margin: 5px;

    p {
      display: none;
    }
  }
}

.marker {
  position: absolute;
  width: 15px;
  height: 15px;
  background-color: red;
  color: white;
  border-radius: 50%;
  text-align: center; /* 水平居中 */
  line-height: 15px; /* 垂直居中（与 height 一致） */
  font-weight: bold;
  user-select: none;
  font-size: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
