import Cookies from 'js-cookie'
// cookie保存的天数
import config from '@/config'
import { forEach, hasOneOf, objEqual } from '@/libs/tools'
import path from '../path/path'
const { title, useI18n } = config
import API from '@/api/index.js'

export const PRE_FIX = serverConfig.APP_CODE;
export const TOKEN_KEY = PRE_FIX + '_access_token'
export const EXPIRES_IN = PRE_FIX+"_expires_in";
export const REFRESH_TOKEN_KEY = PRE_FIX + '_refresh_token'
export const CITY_CODE_KEY = PRE_FIX + '_city_code'
export const CITY_NAME_KEY = PRE_FIX + '_city_name'
export const REG_CODE_KEY = PRE_FIX + '_reg_code'
export const REG_NAME_KEY = PRE_FIX + '_reg_name'
export const ORG_CODE_KEY = PRE_FIX + '_org_code'
export const ORG_CODE = 'org_code'
export const ORG_NAME_KEY = PRE_FIX + '_org_name'
export const ORG_TYPE_KEY = PRE_FIX+"_org_type";

export const USER_ID_KEY = PRE_FIX + '_user_id'
export const USER_JOB_ID_KEY = PRE_FIX+ "_user_job_id";
export const USER_CONFIRM_STATUS_KEY =PRE_FIX+ "_confirm_status";
export const USER_LOGIN_KEY = PRE_FIX + '_user_login'
export const USER_CARD_KEY = PRE_FIX + '_user_card'
export const USER_NAME_KEY = PRE_FIX + '_user_name'
export const USER_ADMIN_KEY = PRE_FIX + '_user_admin'
export const USER_S_ADMIN_KEY = 'bsp_user_s_admin'
export const LOGIN_TIME = 'bsp_login_time'

export const MEUN_KEY = 'bsp_menu'
export const FUNC_PERM_KEY = 'func_perm'
export const ROOT_ROUTER_KEY = 'bsp_root_router'

export const setToken = (token) => {
  // Cookies.set(TOKEN_KEY, token)
  sessionStorage.setItem(TOKEN_KEY, token)
  Cookies.set(TOKEN_KEY, token)
}

export const setConfirmSTatus = (status) => {
  sessionStorage.setItem(USER_CONFIRM_STATUS_KEY, status)
  Cookies.set(USER_CONFIRM_STATUS_KEY, status)
}

export const setLoginTime = (time) => {
  // Cookies.set(LOGIN_TIME, time)
  localStorage.setItem(LOGIN_TIME, time)
}

export const cachePermMenu = (data) => {
  // let accessedRoutes = filterAsyncRouter(data)
  let menuKey = MEUN_KEY + '_' + Cookies.get(USER_ID_KEY)
  if (data) {
    localStorage.setItem(menuKey, JSON.stringify(data))
  }
}

export const getPermMenu = () => {
  let menuKey = MEUN_KEY + '_' + Cookies.get(USER_ID_KEY)
  let menu = localStorage.getItem(menuKey)
  if (menu) {
    return JSON.parse(menu)
  }
  return []
}

export const cacheFuncPerm = (data) => {
  localStorage.setItem(FUNC_PERM_KEY, JSON.stringify(data))
}
export const getFuncPerm = () => {
  let arr = localStorage.getItem(FUNC_PERM_KEY)
  if (arr) {
    try {
      return JSON.parse(arr)
    } catch (e) {
      return []
    }
  }
  return []
}

export const setRootRouter = (name) => {
  Cookies.set(ROOT_ROUTER_KEY, name)
}

export const getRootRouter = () => {
  let value = Cookies.get(ROOT_ROUTER_KEY)
  if (value) return value
  else return ''
}

export const addUserCache = (data) => {
  /* Cookies.set(REFRESH_TOKEN_KEY, data.refresh_token)
  Cookies.set(EXPIRES_IN, data.expires_in)
  Cookies.set(CITY_CODE_KEY, data.cityCode)
  Cookies.set(REG_CODE_KEY, data.regCode)
  Cookies.set(ORG_CODE_KEY, data.orgCode)
  Cookies.set(ORG_NAME_KEY, data.orgName)
  Cookies.set(ORG_TYPE_KEY, data.orgType)
  Cookies.set(USER_ID_KEY, data.id)
  Cookies.set(USER_LOGIN_KEY, data.loginId)
  Cookies.set(USER_CARD_KEY, data.idCard)
  Cookies.set(USER_NAME_KEY, data.name)
  Cookies.set(USER_ADMIN_KEY, data.isAdmin)
  Cookies.set(USER_JOB_ID_KEY, data.jobId)
  Cookies.set(USER_CONFIRM_STATUS_KEY, data.confirmStatus) */

  localStorage.setItem(REFRESH_TOKEN_KEY, data.refresh_token)
  localStorage.setItem(EXPIRES_IN, data.expires_in)
  localStorage.setItem(CITY_CODE_KEY, data.cityCode)
  localStorage.setItem(REG_CODE_KEY, data.regCode)
  localStorage.setItem(ORG_CODE_KEY, data.orgCode)
  localStorage.setItem(ORG_NAME_KEY, data.orgName)
  localStorage.setItem(ORG_TYPE_KEY, data.orgType)
  localStorage.setItem(USER_ID_KEY, data.id)
  localStorage.setItem(USER_LOGIN_KEY, data.loginId)
  localStorage.setItem(USER_CARD_KEY, data.idCard)
  localStorage.setItem(USER_NAME_KEY, data.name)
  localStorage.setItem(USER_ADMIN_KEY, data.isAdmin)
  localStorage.setItem(USER_JOB_ID_KEY, data.jobId)
  // localStorage.setItem(USER_CONFIRM_STATUS_KEY, data.confirmStatus)
  setConfirmSTatus(data.confirmStatus)
}

export const getToken = () => {
  let token = Cookies.get(TOKEN_KEY)
  if (!token) {
    token = sessionStorage.getItem(TOKEN_KEY)
  }
  if (token) return token
  else return false
}

export const getConfirmSTatus = () => {
  let status = Cookies.get(USER_CONFIRM_STATUS_KEY)
  if (!status) {
    status = sessionStorage.getItem(USER_CONFIRM_STATUS_KEY)
  }
  if (status) return status
  else return '0'
}
export const getRefreshToken = () => {
  const token = localStorage.getItem(REFRESH_TOKEN_KEY)
  if (token) return token
  else return false
}
export const getLoginTime = () => {
  const login_time = localStorage.getItem(LOGIN_TIME)
  if (login_time) return login_time
  else return false
}
export const getUserCache = {
  getCityCode: function () {
    let cityCode = localStorage.getItem(CITY_CODE_KEY)
    if (cityCode) return cityCode
    else return ''
  },
  getCityName: function () {
    let value = Cookies.get(CITY_NAME_KEY)
    if (value) return value
    else return ''
  },
  getRegCode: function () {
    let regCode = localStorage.getItem(REG_CODE_KEY)
    if (regCode) return regCode
    else return ''
  },
  getRegName: function () {
    let regName = Cookies.get(REG_NAME_KEY)
    if (regName) return regName
    else return ''
  },
  getOrgCode: function () {
    let orgCode = localStorage.getItem(ORG_CODE_KEY)
    if (orgCode) return orgCode
    else return ''
  },
  getOrgName: function () {
    let orgName = localStorage.getItem(ORG_NAME_KEY)
    if (orgName) return orgName
    else return ''
  },
  getOrgType: function () {
    let orgType = localStorage.getItem(ORG_TYPE_KEY)
    if (orgType) return orgType
    else return ''
  },
  getUserId: function () {
    let userId = localStorage.getItem(USER_ID_KEY)
    if (userId) return userId
    else return ''
  },
  getLoginId: function () {
    let loginId = localStorage.getItem(USER_LOGIN_KEY)
    if (loginId) return loginId
    else return ''
  },
  getJobId: function () {
    let jobId = localStorage.getItem(USER_JOB_ID_KEY)
    if (jobId) return jobId
    else return ''
  },
  getIdCard: function () {
    let idCard = localStorage.getItem(USER_CARD_KEY)
    if (idCard) return idCard
    else return ''
  },
  getUserName: function () {
    let name = localStorage.getItem(USER_NAME_KEY)
    if (name) return name
    else return ''
  },
  getExpiresIn: function () {
    const expires_in = localStorage.getItem(EXPIRES_IN)
    if (expires_in) return expires_in
    else return ''
  },
  isAdmin: function () {
    let isDadmin = localStorage.getItem(USER_ADMIN_KEY)
    if (isDadmin && isDadmin === 'true') return true
    else return false
  },
  isSAdmin: function () {
    let isSAdmin = localStorage.getItem(USER_S_ADMIN_KEY)
    if (isSAdmin && isSAdmin === 'true') return true
    else return false
  }
}

export const clear = () => {
  let menuKey = MEUN_KEY + '_' + Cookies.get(USER_ID_KEY)
  Cookies.remove(TOKEN_KEY)
  sessionStorage.removeItem(TOKEN_KEY)
  localStorage.removeItem(REFRESH_TOKEN_KEY)
  localStorage.removeItem(EXPIRES_IN)
  localStorage.removeItem(LOGIN_TIME)
  localStorage.removeItem(REG_CODE_KEY)
  localStorage.removeItem(ORG_CODE_KEY)
  localStorage.removeItem(ORG_NAME_KEY)
  localStorage.removeItem(ORG_TYPE_KEY)
  localStorage.removeItem(USER_ID_KEY)
  localStorage.removeItem(USER_LOGIN_KEY)
  localStorage.removeItem(USER_CARD_KEY)
  localStorage.removeItem(USER_NAME_KEY)
  localStorage.removeItem(USER_ADMIN_KEY)
  localStorage.removeItem(USER_S_ADMIN_KEY)
  localStorage.removeItem(USER_JOB_ID_KEY)
  localStorage.removeItem(CITY_CODE_KEY)
  localStorage.removeItem(USER_CONFIRM_STATUS_KEY)

  Cookies.remove(ROOT_ROUTER_KEY)
  localStorage.removeItem(menuKey)
  localStorage.removeItem('tagNaveList')
}

export const hasChild = (item) => {
  return item.children && item.children.length !== 0
}

const showThisMenuEle = (item, access) => {
  if (item.meta && item.meta.access && item.meta.access.length) {
    if (hasOneOf(item.meta.access, access)) return true
    else return false
  } else return true
}
/**
 * @param {Array} list 通过路由列表得到菜单列表
 * @returns {Array}
 */
export const getMenuByRouter = (list, access) => {
  let res = []
  forEach(list, (item) => {
    if (!item.meta || (item.meta && !item.meta.hideInMenu)) {
      let obj = {
        icon: (item.meta && item.meta.icon) || '',
        name: item.name,
        meta: item.meta,
        id: item.id,
        path: item.path,
        parentId: item.parentId
      }
      if (
        (hasChild(item) || (item.meta && item.meta.showAlways)) &&
        showThisMenuEle(item, access)
      ) {
        obj.children = getMenuByRouter(item.children, access)
      }
      if (item.meta && item.meta.href) obj.href = item.meta.href
      if (showThisMenuEle(item, access)) res.push(obj)
    }
  })
  return res
}

/**
 * @param {Array} routeMetched 当前路由metched
 * @returns {Array}
 */
export const getBreadCrumbList = (route, homeRoute) => {
  let homeItem = { ...homeRoute, icon: homeRoute.meta && homeRoute.meta.icon?homeRoute.meta.icon:'' }
  let routeMetched = route.matched
  if (routeMetched.some((item) => item.path === homeRoute.path)) {
    return [homeItem]
  }
  let res = routeMetched
    .filter((item) => {
      return item.meta === undefined || !item.meta.hideInBread
    })
    .map((item) => {
      let meta = { ...item.meta }
      if (meta.title && typeof meta.title === 'function') {
        meta.__titleIsFunction__ = true
        meta.title = meta.title(route)
      }
      let obj = {
        icon: (item.meta && item.meta.icon) || '',
        path: item.path,
        meta: meta
      }
      return obj
    })
  res = res.filter((item) => {
    return !item.meta.hideInMenu
  })
  return [{ ...homeItem, to: homeRoute.path }, ...res]
}

export const getRouteTitleHandled = (route) => {
  let router = { ...route }
  let meta = { ...route.meta }
  let title = ''
  if (meta.title) {
    if (typeof meta.title === 'function') {
      meta.__titleIsFunction__ = true
      title = meta.title(router)
    } else title = meta.title
  }
  meta.title = title
  router.meta = meta
  return router
}

export const showTitle = (item, vm) => {
  // console.log(item.meta,useI18n,'item.meta')
  let { title, __titleIsFunction__ } = item.meta
  if (!title) return
  if (useI18n) {
    if (title.includes('{{') && title.includes('}}') && useI18n) {
      title = title.replace(/({{[\s\S]+?}})/, (m, str) =>
        str.replace(/{{([\s\S]*)}}/, (m, _) => vm.$t(_.trim()))
      )
    } else if (__titleIsFunction__) title = item.meta.title
    else title = vm.$t(item.name)
  } else title = (item.meta && item.meta.title) || item.name
  return title
}

/**
 * @description 本地存储和获取标签导航列表
 */
export const setTagNavListInLocalstorage = (list) => {
  // 保留防止重复标签的逻辑
  let tagList = list.filter((item, index) =>
    list.findIndex(i => i.path === item.path) === index);
  localStorage.tagNaveList = JSON.stringify(tagList)
}

export const setAppTagNavListInLocalstorage = (key, list) => {
  localStorage[key] = JSON.stringify(list)
}
/**
 * @returns {Array} 其中的每个元素只包含路由原信息中的name, path, meta三项
 */
export const getTagNavListFromLocalstorage = () => {
  const list = localStorage.tagNaveList
  return list ? JSON.parse(list) : []
}
export const routeHasExistNew = (tagNavList, routeItem) => {
  let len = tagNavList.length
  let res = false
  doCustomTimes(len, index => {
    if (routeEqualNew(tagNavList[index], routeItem)) res = true
  })

  return res

}
export const getAppTagNavListFromLocalstorage = key => {
  const list = localStorage[key]
  return list ? JSON.parse(list) : []
}
/**
 * @param {Array} routers 路由列表数组
 * @description 用于找到路由列表中name为home的对象
 */
export const getHomeRoute = (routers, homeName = 'home') => {
  let i = -1
  let len = routers.length
  let homeRoute = {}
  while (++i < len) {
    let item = routers[i]
    if (item.children && item.children.length) {
      let res = getHomeRoute(item.children, homeName)
      if (res.name) return res
    } else {
      if (item.name === 'home' || item.path === '/home') homeRoute = item
    }
  }
  return homeRoute
}

/**
 * @param {*} list 现有标签导航列表
 * @param {*} newRoute 新添加的路由原信息对象
 * @description 如果该newRoute已经存在则不再添加
 */
export const getNewTagList = (list, newRoute) => {
  const { name, path, meta } = newRoute
  let newList = [...list]
  if (newList.findIndex((item) => item.path === path) >= 0) return newList
  else newList.push({ name, path, meta })
  return newList
}

export const getNewAppTagList = (list, newRoute) => {
  const { name, path, meta, query } = newRoute
  let newList = [...list]
  if (
    newList.findIndex((item) => item.path === path) >= 0 ||
    path === '/app/system'
  ) {
    return newList
  } else newList.push({ name, path, meta, query })
  return newList
}

/**
 * @param {String} url
 * @description 从URL中解析参数
 */
export const getParams = url => {
  const keyValueArr = url.split('?')[1].split('&')
  let paramObj = {}
  keyValueArr.forEach(item => {
    const keyValue = item.split('=')
    paramObj[keyValue[0]] = keyValue[1]
  })
  return paramObj
}

/**
 * @param {Array} list 标签列表
 * @param {String} name 当前关闭的标签的name
 */
export const getNextRoute = (list, route) => {
  let res = {}
  if (list.length === 2) {
    res = getHomeRoute(list)
  } else {
    const index = list.findIndex((item) => routeEqual(item, route))
    if (index === list.length - 1) res = list[list.length - 2]
    else res = list[index + 1]
  }
  return res
}

export const getNextAppRoute = (list, route) => {
  let res = {}
  if (list.length === 1) {
    // res = getHomeRoute(list)
  } else {
    const index = list.findIndex((item) => routeEqual(item, route))
    if (index === list.length - 1) res = list[list.length - 2]
    else res = list[index + 1]
  }
  return res
}

/**
 * @param {Number} times 回调函数需要执行的次数
 * @param {Function} callback 回调函数
 */
export const doCustomTimes = (times, callback) => {
  let i = -1
  while (++i < times) {
    callback(i)
  }
}

/**
 * @param {Object} file 从上传组件得到的文件对象
 * @returns {Promise} resolve参数是解析后的二维数组
 * @description 从Csv文件中解析出表格，解析成二维数组
 */
export const getArrayFromFile = (file) => {
  let nameSplit = file.name.split('.')
  let format = nameSplit[nameSplit.length - 1]
  return new Promise((resolve, reject) => {
    let reader = new FileReader()
    reader.readAsText(file) // 以文本格式读取
    let arr = []
    reader.onload = function (evt) {
      let data = evt.target.result // 读到的数据
      let pasteData = data.trim()
      arr = pasteData.split((/[\n\u0085\u2028\u2029]|\r\n?/g)).map(row => {
        return row.split('\t')
      }).map(item => {
        return item[0].split(',')
      })
      if (format === 'csv') resolve(arr)
      else reject(new Error('[Format Error]:你上传的不是Csv文件'))
    }
  })
}

/**
 * @param {Array} array 表格数据二维数组
 * @returns {Object} { columns, tableData }
 * @description 从二维数组中获取表头和表格数据，将第一行作为表头，用于在iView的表格中展示数据
 */
export const getTableDataFromArray = (array) => {
  let columns = []
  let tableData = []
  if (array.length > 1) {
    let titles = array.shift()
    columns = titles.map(item => {
      return {
        title: item,
        key: item
      }
    })
    tableData = array.map(item => {
      let res = {}
      item.forEach((col, i) => {
        res[titles[i]] = col
      })
      return res
    })
  }
  return {
    columns,
    tableData
  }
}

export const findNodeUpper = (ele, tag) => {
  if (ele.parentNode) {
    if (ele.parentNode.tagName === tag.toUpperCase()) {
      return ele.parentNode
    } else {
      return findNodeUpper(ele.parentNode, tag)
    }
  }
}

export const findNodeUpperByClasses = (ele, classes) => {
  let parentNode = ele.parentNode
  if (parentNode) {
    let classList = parentNode.classList
    if (classList && classes.every(className => classList.contains(className))) {
      return parentNode
    } else {
      return findNodeUpperByClasses(parentNode, classes)
    }
  }
}

export const findNodeDownward = (ele, tag) => {
  const tagName = tag.toUpperCase()
  if (ele.childNodes.length) {
    let i = -1
    let len = ele.childNodes.length
    while (++i < len) {
      let child = ele.childNodes[i]
      if (child.tagName === tagName) return child
      else return findNodeDownward(child, tag)
    }
  }
}

export const showByAccess = (access, canViewAccess) => {
  return hasOneOf(canViewAccess, access)
}

/**
 * @description 根据name/params/query判断两个路由对象是否相等
 * @param {*} route1 路由对象
 * @param {*} route2 路由对象
 */
export const routeEqual = (route1, route2) => {
  const params1 = route1.params || {}
  const params2 = route2.params || {}
  const query1 = route1.query || {}
  const query2 = route2.query || {}
  return (
    route1.path === route2.path &&
    objEqual(params1, params2) &&
    objEqual(query1, query2)
  )
}

export const routeEqualNew = (route1, route2) => {
  return route1.path === route2.path
}

/**
 * 判断打开的标签列表里是否已存在这个新添加的路由对象
 */
export const routeHasExist = (tagNavList, routeItem) => {
  let len = tagNavList.length
  let res = false
  doCustomTimes(len, (index) => {
    if (routeEqual(tagNavList[index], routeItem)) res = true
  })
  return res
}



export const localSave = (key, value) => {
  localStorage.setItem(key, value)
}

export const localRead = (key) => {
  return localStorage.getItem(key) || ''
}

// scrollTop animation
export const scrollTop = (el, from = 0, to, duration = 500, endCallback) => {
  if (!window.requestAnimationFrame) {
    window.requestAnimationFrame =
      window.webkitRequestAnimationFrame ||
      window.mozRequestAnimationFrame ||
      window.msRequestAnimationFrame ||
      function (callback) {
        return window.setTimeout(callback, 1000 / 60)
      }
  }
  const difference = Math.abs(from - to)
  const step = Math.ceil((difference / duration) * 50)

  const scroll = (start, end, step) => {
    if (start === end) {
      endCallback && endCallback()
      return
    }

    let d = start + step > end ? end : start + step
    if (start > end) {
      d = start - step < end ? end : start - step
    }

    if (el === window) {
      window.scrollTo(d, d)
    } else {
      el.scrollTop = d
    }
    window.requestAnimationFrame(() => scroll(d, end, step))
  }
  scroll(from, to, step)
}

/**
 * @description 根据当前跳转的路由设置显示在浏览器标签的title
 * @param {Object} routeItem 路由对象
 * @param {Object} vm Vue实例
 */
export const setTitle = (routeItem, vm) => {
  const handledRoute = getRouteTitleHandled(routeItem)
  const pageTitle = showTitle(handledRoute, vm)
  const resTitle = pageTitle ? `${title} - ${pageTitle}` : title
  window.document.title = resTitle
}


// 获取时间格式转换   2020-10-22 12:30:30 这种格式
// time   表示需要转化的时间  , cFormat  需要转为格式
export const formatDateparseTime = (time, cFormat) => {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string') {
      if (/^[0-9]+$/.test(time)) {
        // support "1548221490638"
        time = parseInt(time)
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), '/')
      }
    }

    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return time_str
}
const charSet = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
export const getUuid=(length = 16)=> {
  let charSetLen = charSet.length
  let outStr = ''
  for (let i = 0; i < length; i++) {
    outStr = outStr.concat(
      charSet.charAt(Math.floor(Math.random(0, 1) * charSetLen))
    )
  }
  return outStr
}


export const ensureNonNullStrings=(obj)=> {
  for (let key in obj) {
      if (obj.hasOwnProperty(key) && obj[key] === null) {
      obj[key] = '';
      }
  }
  return obj;
  }
  export const removeNullFields=(obj)=> {
  return Object.fromEntries(
      Object.entries(obj).filter(([key, value]) => value !== null)
  );
  }


      // 获取地址参数
      export const    parseQuery=(url)=> {
        let o = {}
        let queryString = url.split("?")[1]
        if (queryString) {
            queryString=queryString.split("#")[0];
            queryString
                .split('&')
                .forEach(item => {
                    let [key, val] = item.split('=')
                    val = val ? decodeURI(val) : true
                    //          转码         无值赋值true
                    if (o.hasOwnProperty(key)) {
                        //   已有属性转为数组
                        o[key] = [].concat(o[key], val)
                    } else {
                        o[key] = val
                    }
                })
        }
        return o
      }


/**
 * @description 身份证校验
 * @param {String} id 身份证号码
 */
export const identityIsTrue = (id) => {
  const paternMainLand =
    /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}X)$)$/
  const patternHongkong =
    /^((\s?[A-Za-z])|([A-Za-z]{2}))\d{6}(\([0-9aA]\)|[0-9aA])$/
  const patternTaiwan = /^[a-zA-Z][0-9]{9}$/
  const patternMacao = /^[1|5|7][0-9]{6}\([0-9Aa]\)/
  let isMainLand = paternMainLand.test(id)
  let isHongKong = patternHongkong.test(id)
  let isTaiwan = patternTaiwan.test(id)
  let isMacao = patternMacao.test(id)
  return isMainLand || isHongKong || isTaiwan || isMacao
}

/**
 * @description: 统一输入验证方法
 */
export const validatorFunc = (val, mess, reg, cb) => {
  if (reg && reg.test(val)) {
  } else if (reg && Boolean(val)) {
    cb(new Error(`输入的${mess}非法`))
  }
  cb()
}

// 统一长度验证
export const lengthLimited = {
  type: 'string',
  max: 20,
  message: '不能超过20个字符',
  trigger: 'blur'
}

// 验证密码有效性
export function isPassword (str) {
  str = str || ''
  str = str.trim()
  return str.length >= 6 && str.length <= 12
}

//  树转化为数组
export const flatTree = (tree, childrenKey = 'children') => {
  if (Array.isArray(tree)) {
    return tree.reduce((res, child) => res.concat(flatTree(child)), [])
  }
  if (tree[childrenKey] && tree[childrenKey].length) {
    let subTrees = tree[childrenKey].reduce(
      (res, child) => res.concat(flatTree(child)),
      []
    )
    return [tree].concat(subTrees)
  } else {
    return [tree]
  }
}

// 数组转化为树
export const arrayToTree = (arr, isParent, childrenKey = 'children') => {
  let copyArr = arr.slice()
  let rootArr = arr.slice()
  for (let p of copyArr) {
    for (let c of copyArr) {
      let flag = isParent(p, c)
      if (flag) {
        if (!Array.isArray(p[childrenKey])) {
          p[childrenKey] = []
        }
        p[childrenKey].push(c)
        let i = rootArr.indexOf(c)
        rootArr.splice(i, 1)
      }
    }
  }
  return rootArr
}

// 去除对象中的空字段
export const deleteNullField = (obj) => {
  if (!(typeof obj === 'object')) {
    return
  }
  for (let key in obj) {
    if (
      obj.hasOwnProperty(key) &&
      (obj[key] === null || obj[key] === undefined || obj[key] === '')
    ) {
      delete obj[key]
    }
  }
  return obj
}

// 深拷贝
export const deepCopy = (obj) => {
  return JSON.parse(JSON.stringify(obj))
}

export const throttle = (fn, delay = 1000) => {
  let timer
  return function (...args) {
    clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

export const disabledChildren = (data) => {
  if (!data) return
  data = data.filter((it) => !it.disabled)
  for (let child of data) {
    child.children = disabledChildren(child.children)
  }
  return data
}

// // 通过后台的菜单树，构造路由
export const filterAsyncRouter = (routers) => {
  routers = routers.filter((it) => !it.disabled)
  const accessedRoutes = routers.map((it) => {
    if (it.children && it.children.length > 0) {
      it.children = filterAsyncRouter(it.children)
    }
    return transFormData(it)
  })
  return accessedRoutes
}

export const transFormData = (it) => {
  let data = {
    name: it.code,
    path: it.url,
    id: it.id,
    meta: {
      icon: it.imgPath || 'ios-app',
      title: it.name,
      showMenu: it.showMenu
    },
    children: it.children
  }
  return data
}

export const getDaysBetweenDates = (date1, date2) => {
  // 将日期字符串转换为时间戳
  let timestamp1 = new Date(date1).getTime()
  let timestamp2 = new Date(date2).getTime()

  // 计算两个时间戳之间的天数
  let days = Math.ceil(Math.abs(timestamp2 - timestamp1) / (1000 * 3600 * 24))

  return days
}

// 解析标签页参数
export const getTabsParams = (data) => {
  const params = data.split('&') // 分割成数组
  let realDate = {}
  for (let i = 0; i < params.length; i++) {
    const [key, value] = params[i].split('=') // 分割键和值
    realDate[key] = value
  }
  return realDate
}

export const getUploadUrl = () => {
  let uploadUrl = API.DmsApi.DMS_UPLOAD_GOFAST
  if (serverConfig.uploadType && serverConfig.uploadType == '2') {
    uploadUrl = API.DmsApi.DMS_UPLOAD_LCY
  } else {
    return API.UploadApi.SUNDUN_FILE_UPLOAD
  }
  let testUrl = uploadUrl.substring(1)
  uploadUrl = serverConfig.dmsUrl + testUrl.substring(testUrl.indexOf('/')) + '?a=1'
  return uploadUrl
}
// 查询当前的操作系统
export const GetOSInfo = () => {
  var _pf = navigator.platform
  if (String(_pf).indexOf('Linux') > -1) {
    return true
  } else {
    return false
  }
}
export const getPdfUploadUrl = () => {
  let uploadUrl = API.DmsApi.DMS_UPLOAD_GOFAST
  if (serverConfig.uploadType && serverConfig.uploadType == '2') {
    uploadUrl = API.DmsApi.DMS_UPLOAD_LCY
  }
  let testUrl = uploadUrl.substring(1)
  uploadUrl = serverConfig.dmsUrl + testUrl.substring(testUrl.indexOf('/')) + '?a=1'
  return uploadUrl
}

// 更改路径地址
export const goFastUrlDeal = (file) => {
  if (!file.path.includes('pdf')) {
    return file.domain + file.path
  } else {
    return file.url
  }
}

// 字段转驼峰
export const toCamel = (name) => {
  return name.replace(/\_(\w)/g, function (all, letter) {
    return letter.toUpperCase()
  })
}
// 对象转驼峰
export const objToCamel = (obj) => {
  let newObj = {}
  for (let key in obj) {
    let newKey = toCamel(key)
    newObj[newKey] = obj[key]
  }
  return newObj
}

/** 数据类型转换（Base64转file)
     * author:fangyeuming
     * @param  dataUrl(base64)
     * @param  filename(文件名)
     */
export const dataURLtoFile = (dataUrl, filename) => {
  let arr = dataUrl.split(',')
  let mime = arr[0].match(/:(.*?);/)[1]
  let bstr = atob(arr[1])
  let n = bstr.length
  let u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new File([u8arr], filename, {
    type: mime
  })
}


// 转换空对象
export const normalizeObject = (obj) => {
  return Object.entries(obj).reduce((acc, [key, value]) => {
    acc[key] = value === null || value === 'null' || value === undefined || value === '' ? '-' : value
    return acc
  }, {})
}

/**
 * 统一处理空值显示
 * @param {*} value - 需要处理的值
 * @param {string} defaultValue - 默认显示值，默认为 '-'
 * @returns {*} 处理后的值
 * @description 统一处理 null、undefined、空字符串、'null' 字符串等空值情况
 */
export const formatValue = (value, defaultValue = '-') => {
  if (value === null || value === undefined || value === '' || value === 'null') {
    return defaultValue
  }
  return value
}
