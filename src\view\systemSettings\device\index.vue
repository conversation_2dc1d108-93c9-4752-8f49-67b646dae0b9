<template>
	<div>
    <div class="content-wrap">
      <div class="content-wrap-main" style="width: 100%;">
        <div class="wrap-cont">
          <s-DataGrid ref="grid" funcMark="szptsbgllb" :customFunc="true" :params="params" v-if="!showData" @resetGird=resetGird>
            <template v-slot:query:area_id="{ condi, row, index }">
              <Row>
                <Col span="24">
                  <el-cascader
                  :options="areaData"
                  clearable
                  :show-all-levels="false"
                  filterable
                  @change="changeArea"
                  popper-class="areaClassName"
                  :value="params.area_id"
                  style="width: 100%"
                  :props="{ checkStrictly: true }"
                ></el-cascader>
                </Col>
              </Row>
            </template>
            <template slot="customHeadFunc" slot-scope="{ func }">
              <Button type="primary" v-if="func.includes(globalAppCode + ':szptsbgllb:add')" @click.native="addRegion('add')">新增</Button>
              <Button type="default" v-if="func.includes(globalAppCode + ':szptsbgllb:import')" style="border: 1px solid #57a3f3; color: #57a3f3;" @click.native="triggerFileInput">导入</Button>
                <input
                  type="file"
                  ref="fileInput"
                  style="display: none"
                  accept=".xls,.xlsx"
                  @change="handleFileChange"
                >
              <Button type="default" v-if="func.includes(globalAppCode + ':szptsbgllb:tem')" style="border: 1px solid #57a3f3; color: #57a3f3;" @click.native="temEvent">模板</Button>
            </template>
            <template slot="customRowFunc" slot-scope="{ func, row, index }">
              <Button type="primary" v-if="func.includes(globalAppCode + ':szptsbgllb:edit')" @click.native="addRegion('edit',row)" >编辑</Button>
              <Button type="primary" v-if="func.includes(globalAppCode + ':szptsbgllb:detail')" @click.native="addRegion('detail',row)">详情</Button>
              <Button type="error" v-if="func.includes(globalAppCode + ':szptsbgllb:delete')" @click.native="deleleChange(row)">删除</Button>
              </template>
          </s-DataGrid>
        </div>
      </div>

      <Modal v-model="regionModal" width="70%" :closable="false">
        <div class="flow-modal-title" slot="header">
          <span style="font-size: 17px;">{{ regionTitle }}</span>
          <span @click="cancelRegion" style="position: absolute; right: 6px;cursor: pointer;">
              <i class="ivu-icon ivu-icon-ios-close"></i>
          </span>
        </div>
       <div>
        <Form ref="formData" :model="formData" :rules="ruleValidate" :label-width="120" label-colon>
          <p class="title">设备信息</p>
          <Row>
            <Col span="12">
              <FormItem label="设备名称" prop="deviceName" :rules="[{ trigger: 'blur,change', message: '设备名称为必填', required: true }]">
                <Input type="text" v-model="formData.deviceName"/>
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="设备类型" prop="deviceTypeId" :rules="[{ trigger: 'blur,change', message: '设备类型为必选', required: true }]">
                <s-dicgrid v-model="formData.deviceTypeId" @values="getDeviceType" :isSearch="true" dicName="ZD_SBLXDM" />
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <FormItem label="所属区域" prop="areaId" >
                <el-cascader
                  :options="areaData"
                  clearable
                  :show-all-levels="false"
                  filterable
                  @change="changeAreas"
                  popper-class="areaClassName"
                  :value="formData.areaId"
                  style="width: 100%"
                  :props="{ checkStrictly: true }"
                ></el-cascader>
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="设备状态" prop="deviceStatus">
                <s-dicgrid v-model="formData.deviceStatus" @values="getDeviceStatus" :isSearch="true" dicName="ZD_SBZTDM" />
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <FormItem label="IP地址" prop="ipAddress">
                <Input type="text" v-model="formData.ipAddress"/>
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="MAC地址" prop="macAddress">
                <Input type="text" v-model="formData.macAddress"/>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <FormItem label="通道名称" prop="channelName">
                <Input type="text" v-model="formData.channelName"/>
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="通道编码" prop="channelId">
                <Input type="text" v-model="formData.channelId"/>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="24">
              <FormItem label="关联摄像机" prop="refDeviceId">
                <div class="camera-selector" style="border: 1px solid #dcdee2; border-radius: 4px; min-height: 32px; padding: 8px 12px;">
                  <div v-if="selectedCameras.length > 0" class="selected-cameras">
                    <Tag
                      v-for="camera in selectedCameras"
                      :key="camera.id"
                      closable
                      @on-close="removeCamera(camera)"
                      :title="camera.title"
                    >
                      {{ camera.title }}
                    </Tag>
                  </div>
                  <Poptip
                    trigger="click"
                    placement="bottom-start"
                    width="450"
                    :transfer="false"
                  >
                    <Button
                      type="dashed"
                      size="default"
                      icon="ios-add"
                      style="width: 100%;"
                    >
                      {{ selectedCameras.length > 0 ? '继续选择摄像机' : '请选择关联摄像机' }}
                    </Button>
                    <div slot="content" class="camera-tree-container">
                      <div class="tree-header">
                        请选择摄像机（支持多选，选择父节点会自动选择所有子节点）
                      </div>
                      <el-tree
                        ref="cameraTree"
                        :data="cameraTreeData"
                        node-key="id"
                        :show-checkbox="true"
                        :check-strictly="false"
                        :default-expand-all="true"
                        :default-checked-keys="formData.refDeviceId"
                        @check="handleCameraCheck"
                      >
                      </el-tree>
                    </div>
                  </Poptip>
                </div>
              </FormItem>
            </Col>
          </Row>
        </Form>
       </div>
        <div slot="footer">
          <Button @click="cancelRegion" class="cancle_btn">取消</Button>
          <Button type="primary" @click="okRegion" class="sure_btn">确定</Button>
        </div>
      </Modal>

      <Modal v-model="regionDetailModal" width="50%" :closable="false">
        <div class="flow-modal-title" slot="header">
          <span style="font-size: 17px;">设备详情</span>
          <span @click="cancelRegionDetail" style="position: absolute; right: 6px;cursor: pointer;">
              <i class="ivu-icon ivu-icon-ios-close"></i>
          </span>
        </div>
        <div>
            <div class="djxx-wrap" style="margin-bottom: 20px; border-botton: 1px solid #000">
              <!-- <p class="djxx-title">设备信息</p> -->
              <Row>
                <Col span="4">设备名称</Col>
                <Col span="8">{{detailData.deviceName ? detailData.deviceName : '-'}}</Col>
                <Col span="4">设备类型</Col>
                <Col span="8">{{detailData.deviceTypeIdName ? detailData.deviceTypeIdName : '-'}}</Col>
              </Row>
              <Row>
                <Col span="4">所属区域</Col>
                <Col span="8">
                  {{detailData.areaName ? detailData.areaName : '-'}}
                </Col>
                <Col span="4">设备状态</Col>
                <Col span="8">{{detailData.deviceStatusName ? detailData.deviceStatusName : '-'}}</Col>
              </Row>
              <Row>
                <Col span="4">IP地址</Col>
                <Col span="8">{{detailData.ipAddress ? detailData.ipAddress : '-'}}</Col>
                <Col span="4">MAC地址</Col>
                <Col span="8">{{detailData.macAddress ? detailData.macAddress : '-'}}</Col>
              </Row>
              <Row>
                <Col span="4">通道名称</Col>
                <Col span="8">{{detailData.channelName ? detailData.channelName : '-'}}</Col>
                <Col span="4">通道编码</Col>
                <Col span="8">{{detailData.channelId ? detailData.channelId : '-'}}</Col>
              </Row>
              <Row>
                <Col span="4">关联摄像头</Col>
                <Col span="20">{{detailData.refDeviceName ? detailData.refDeviceName : '-'}}</Col>
              </Row>
            </div>
        </div>
        <div slot="footer">
          <Button @click="cancelRegionDetail" class="save">取消</Button>
          <!-- <Button type="primary" @click="okRegion" class="save">提交</Button> -->
        </div>
      </Modal>
    </div>
  </div>
  </template>

<script>
import { userSelector } from 'sd-user-selector'
import { sDataGrid } from 'sd-data-grid'
import { mapActions } from 'vuex'
import areaTree from '../components/area-tree.vue'
import { getToken } from '@/libs/util'
import axios from 'axios';
import { Col, Row } from 'view-design'
export default {
  components: {
    sDataGrid,
    areaTree,
    userSelector
  },
  data () {
    return {
      params: {},
      regionType: 'add',
      originTreeData: [],
      treeData: [],
      showData: false,
      columns: [
        {
          name: '序号',
          type: 'index',
          width: 60,
          align: 'center'
        },
        { title: '区域名称', key: 'areaName', align: 'center', tooltip: true },
        { title: '区域编码', key: 'areaCode', align: 'center', tooltip: true },
        { title: '父节点名称', key: 'orgName', align: 'center', tooltip: true },
        { title: '区域类型', align: 'center', tooltip: true, key: 'areaType' },
        { title: '创建时间', align: 'center', tooltip: true, key: 'addTime' },
        { title: '更新时间', align: 'center', tooltip: true, key: 'updateTime' },
        { title: '操作', slot: 'action', width: 200, align: 'center' },
      ],
      dataTable: [],
      page: {
        pageNo: 1,
        pageSize: 10,
        msgType: '01',
        orgCode: this.$store.state.common.orgCode
      },
      total: 0,
      regionModal: false,
      regionTitle: '新增设备',
      ruleValidate: {},
      formData: {
        areaName: '',
        parentName: '',
        parentId: '',
        areaType: '',
        refDeviceId: [], // 关联摄像机ID数组
        areaId: null,
        areaPrisonRoomSaveDto: {
          imprisonmentAmount: '',
          status: '',
          roomSex: '',
          roomType: '',
          roomArea: '',
          avgBedsArea: ''
        },
        userIdZb: '',
        userNameZb: '',
        userIdXb: '',
        userNameXb: ''
      },
      defaultFill: true,
      currentData: {
        orgCode: '',
        currentID: ''
      },
      regionDetailModal: false,
      detailData: {},
      defaultTree: {
        defaultId: '',
        defaultOrgCode: ''
      },
      monitoringRoomForm: false, // 区域类型选择‘监室’后显示监室属性标签
      allowAdding: false,
      currentItem: {},
      areaData: [],
      cameraTreeData: [], // 摄像头树数据
      selectedCameras: [] // 已选择的摄像机列表
      // datar:['1100001130100010001','1100001130100020001','1100001130100320002']
    }
  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'authGetRequestBlob']),
    // 获取列表
    getList () {
      let orgCode = this.$store.state.common.orgCode
      this.authGetRequest({ url: this.$path.bsp_system_getAreaTree, params: { orgCode: orgCode } }).then(resp => {
        if (resp.success && resp.data) {
          let areaList = this.transformData(resp.data)
          this.areaData = areaList
          console.log(this.areaData, 'this.areaData')
        } else {
          this.errorModal({ content: '数据请求失败，请联系管理员' })
        }
      })
    },
    // 获取摄像头树数据
    getCameraTreeData() {
      let orgCode = this.$store.state.common.orgCode
      this.authGetRequest({ url: this.$path.bsp_system_getAreaCameraTree, params: { orgCode: orgCode } }).then(resp => {
        if (resp.success && resp.data) {
          let cameraList = this.transformCameraData(resp.data)
          this.cameraTreeData = cameraList
          console.log(this.cameraTreeData, 'this.cameraTreeData')
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: '摄像头数据请求失败，请联系管理员'
          })
        }
      })
    },
    // 转换摄像头数据为el-tree需要的格式
    transformCameraData(data) {
      return data.map((item) => {
        const newItem = {
          id: item.id,
          label: item.name, // el-tree使用label作为显示文本
          title: item.name,
          value: item.id,
          areaType: item.areaType
        }

        if (item.children && item.children.length > 0) {
          newItem.children = this.transformCameraData(item.children)
        }

        return newItem
      })
    },

    // 处理摄像机选择
    handleCameraCheck() {
      // 获取所有选中的节点
      const checkedNodes = this.$refs.cameraTree.getCheckedNodes()

      // 只保留叶子节点（实际的摄像头节点）用于显示和提交
      const leafNodes = checkedNodes.filter(node => !node.children || node.children.length === 0)

      // 更新选中的摄像机列表（只显示叶子节点）
      this.selectedCameras = leafNodes.map(node => ({
        id: node.id,
        title: node.label
      }))

      // 更新表单数据（只提交叶子节点的ID）
      this.formData.refDeviceId = leafNodes.map(node => node.id)

      console.log('选中的摄像机:', this.selectedCameras)
      console.log('选中的节点ID:', this.formData.refDeviceId)
    },

    // 移除选中的摄像机
    removeCamera(camera) {
      // 从选中列表中移除
      this.selectedCameras = this.selectedCameras.filter(item => item.id !== camera.id)

      // 更新表单数据
      this.formData.refDeviceId = this.selectedCameras.map(item => item.id)

      // 更新树的选中状态
      if (this.$refs.cameraTree) {
        this.$refs.cameraTree.setCheckedKeys(this.formData.refDeviceId)
      }
    },

    // 初始化选中的摄像机（编辑时使用）
    initSelectedCameras() {
      if (!this.formData.refDeviceId || this.formData.refDeviceId.length === 0) {
        this.selectedCameras = []
        return
      }

      // 递归查找摄像机名称
      const findCameraNames = (nodes, ids) => {
        const result = []
        for (const node of nodes) {
          if (ids.includes(node.id)) {
            result.push({
              id: node.id,
              title: node.label || node.name
            })
          }
          if (node.children && node.children.length > 0) {
            result.push(...findCameraNames(node.children, ids))
          }
        }
        return result
      }

      // 等待摄像头数据加载完成后初始化
      this.$nextTick(() => {
        if (this.cameraTreeData.length > 0) {
          this.selectedCameras = findCameraNames(this.cameraTreeData, this.formData.refDeviceId)

          // 设置树的选中状态
          setTimeout(() => {
            if (this.$refs.cameraTree) {
              this.$refs.cameraTree.setCheckedKeys(this.formData.refDeviceId)
            }
          }, 100)
        }
      })
    },
    transformData (data) {
      return data.map((item) => {
        const newItem = {
          ...item, // 保留原始所有字段
          value: item.id, // 新增value字段
          label: item.name // 新增label字段
        }

        if (item.children && item.children.length > 0) {
          newItem.children = this.transformData(item.children) // 递归处理子节点
        }

        return newItem
      })
    },
    getdefaultTableData (id, orgCode) {
      this.postRequest({ url: this.$path.bsp_system_page,
        params: {
          orgCode: orgCode,
          id: id,
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize
        }
      }).then(resp => {
        if (resp.success && resp.data) {
          this.dataTable = resp.data.list
          this.total = resp.data.list.length
        }
      })
    },
    getNo (pageNo) {
      this.$set(this.page, 'pageNo', pageNo)
      this.getTableData()
    },
    getSize (pageSize) {
      this.$set(this.page, 'pageSize', pageSize)
      this.getTableData()
    },
    search () {
      // console.log(this.page,'search')
      this.getTableData()
    },
    getTableData () {
      console.log(this.currentData, 'currentData')
      this.postRequest({
        url: this.$path.bsp_system_page,
        params: {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          id: this.currentData.currentID,
          orgCode: this.currentData.orgCode
          // areaName: this.currentData.areaName,
          // areaCode: this.currentData.areaCode,
          // areaType: this.currentData.areaType
        }
      }).then(res => {
        console.log(res)
      })
    },
    addYp (row) {
      console.log(row)
    },
    handleXzChange (item, data) {
      console.log(item, data)
      this.currentItem = data[0]
      // this.formData.parentId = data[0].id
      this.showData = true
      this.$set(this.params, 'parent_id', data[0].id)
      this.$set(this.formData, 'parentId', data[0].id)
      setTimeout(() => {
        this.showData = false
        this.allowAdding = true
        // this.formData.parentId = data[0].id
        // if(item && !item.isParent) { // 如果点击的当前节点有子节点 就传当前节点自己的id
        //   this.formData.parentId = data[0].parentId
        // } else {
        //   this.allowAdding = true
        //   this.formData.parentId = data[0].id
        // }
        this.currentData.orgCode = data[0].orgCode
        this.currentData.currentID = data[0].id
        this.currentData.parentName = data[0].name
        this.currentData.areaName = data[0].areaName
        this.currentData.areaCode = data[0].areaCode
        this.currentData.areaType = data[0].areaType
        let orgCode = data[0].orgCode
        let id = data[0].id
      }, 100)

      // this.postRequest({ url: this.$path.bsp_system_page, params: {
      //   orgCode: orgCode,
      //   id: id,
      //   pageNo: this.page.pageNo,
      //   pageSize: this.page.pageSize
      //  }
      // }).then(resp => {
      //   if(resp.success && resp.data) {
      //     this.dataTable = resp.data.list
      //     this.total = resp.data.list.length
      //   }
      // })
      console.log(this.params, 'params')
    },
    addRegion (type, row) {
      // 获取摄像头树数据
      this.getCameraTreeData()
      this.formData = {}
      this.regionType = type
      if (type == 'add') {
        console.log(this.currentItem, 'currentItem')
        console.log(this.formData, 'add')
        this.regionTitle = '新增设备'
        this.formData.refDeviceId = [] // 初始化关联摄像机为空数组
        this.selectedCameras = [] // 清空选中的摄像机
        this.regionModal = true
      } else if (type == 'edit') {
        this.regionTitle = '设备信息编辑'
        this.getRegionDetail(row)
      } else if (type == 'detail') {
        this.getRegionDetail(row)
      }
    },
    getRegionDetail (row) {
      console.log(row)
      this.authGetRequest({
        url: this.$path.bsp_device_get,
        params: {
          id: row.id
        }
      }).then(res => {
        console.log(res, 'res')
        console.log(this.regionType, 'this.regionType')
        if (res.success && res.data) {
          if (this.regionType == 'edit') {
            // 赋值基础数据，Vue 会合并，areaId 会被覆盖
            this.formData = { ...this.formData, ...res.data };

            // 构造区域路径
            let areaPath = [];
            if (res.data.allParentId) {
              // 确保 split('#') 后过滤掉可能的空字符串
              areaPath = res.data.allParentId.slice(6).split('#').filter(id => id);
            }
            if (res.data.areaId) {
              areaPath.push(res.data.areaId);
            }

            // 显式赋值 areaId，确保是正确的数组路径
            this.formData.areaId = areaPath;

            // 处理关联摄像机数据
            if (res.data.refDeviceId) {
              this.formData.refDeviceId = res.data.refDeviceId.split(',')
              // 初始化选中的摄像机显示列表
              this.initSelectedCameras()
            } else {
              this.formData.refDeviceId = []
              this.selectedCameras = []
            }

            console.log(this.formData.areaId, 'this.formData.areaId')
            this.regionModal = true
          } else if (this.regionType == 'detail') {
            this.detailData = res.data

            let detailAreaPath = [];
            if (res.data.allParentId) {
              detailAreaPath = res.data.allParentId.slice(6).split('#').filter(id => id);
            }
            if (res.data.areaId) {
              detailAreaPath.push(res.data.areaId);
            }
            // 使用 $set 保证响应性，因为 detailData 可能没有预定义 areaId
            this.$set(this.detailData, 'areaId', detailAreaPath);
            console.log('详情时区域路径:', this.detailData.areaId); // 调试日志
            this.regionDetailModal = true;
          }
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: res.msg || '接口操作失败!'
          })
        }
      })
    },
    okRegion () {
      console.log(this.formData, '1')
      this.$refs['formData'].validate((valid) => {
        if (valid) {
          // 准备要发送的数据
          const payload = { ...this.formData }; // 复制一份formData

          // 处理关联摄像机数据，将 refDeviceId 数组转换为逗号分隔的字符串
          if (payload.refDeviceId && payload.refDeviceId.length > 0) {
            payload.refDeviceId = payload.refDeviceId.join(','); // 直接修改payload中的refDeviceId
          } else {
            payload.refDeviceId = ''; // 如果为空，则设置为空字符串
          }

          if (this.regionType == 'add') {
            this.saveData(payload)
          } else {
            this.updateEvent(payload)
          }

          console.log('保存')
        } else {
          this.loading = false
          this.$Message.error('请填写完整!!')
        }
      })
    },
    saveData (payload) {
      console.log(this.formData, '2')
      this.authPostRequest({
        url: this.$path.bsp_device_add,
        params: payload
      }).then(res => {
        console.log(res, '新增')
        if (res.success) {
          this.regionModal = false
          this.$Modal.success({
            title: '温馨提示',
            content: '操作成功!',
            onOk: () => {
              this.on_refresh_table()
            }
          })
        } else {
          this.regionModal = false
          this.$Modal.success({
            title: '温馨提示',
            content: res.msg || '操作失败!'
          })
        }
      })
    },
    updateEvent (payload) {
      console.log(this.formData, '2')
      payload["areaId"] = ((typeof payload["areaId"]) === "object") ? payload["areaId"][payload["areaId"].length - 1] : payload["areaId"];
      this.authPostRequest({
        url: this.$path.bsp_device_update,
        params: payload
      }).then(res => {
        console.log(res, '编辑')
        if (res.success) {
          this.regionModal = false
          this.formData = {}
          // this.getList()
          this.selectedCameras = []; // 清空选中的摄像机显示
          this.on_refresh_table()
        }
      })
    },
    cancelRegion () {
      this.regionModal = false
    },
    cancelRegionDetail () {
      this.regionDetailModal = false
    },
    deleleChange (row) {
      this.$Modal.confirm({
        title: '温馨提示',
        content: '请确认是否删除？',
        onOk: () => {
          this.authGetRequest({
            url: this.$path.bsp_device_delete,
            params: {
              ids: row.id
            }
          }).then(res => {
            console.log(res, '删除')
            if (res.success) {
              this.on_refresh_table()
            } else {
              this.$Modal.error({
                title: '温馨提示',
                content: res.msg || '操作失败'
              })
            }
          })
        }
      })
    },
    searchBtn () {
      let params = {
        orgCode: this.currentData.orgCode || this.defaultTree.defaultId,
        id: this.currentData.currentID || this.defaultTree.defaultOrgCode,
        pageNo: this.page.pageNo,
        pageSize: this.page.pageSize,
        areaCode: this.page.bar ? this.page.bar : '',
        areaName: this.page.title ? this.page.title : ''
      }
      console.log(this.page)
      this.postRequest({ url: this.$path.bsp_system_page, params: params }).then(resp => {
        // console.log(res);
        if (resp.success && resp.data.list) {
          this.dataTable = resp.data.list
          this.total = resp.data.list.length
        }
      })
    },
    getqylxName (data) {
      // console.log(data,'data');
      data.forEach(item => {
        console.log(item, 'item')
        if (item.code == '0003') {
          this.monitoringRoomForm = true
        }
      })
    },
    getDeviceStatus (data) {
      console.log(data, '设备状态')

      // this.formData.areaPrisonRoomSaveDto.roomSex = data[0].name
    },
    getJslxName (data) {
      console.log(data, '监室事务')
      this.formData.areaPrisonRoomSaveDto.roomType = data[0].name
    },
    changeArea (data) {
      console.log(data, '区域类型')
      if (data) {
        this.$nextTick(() => {
          this.showData = true
        })

        setTimeout(() => {
          this.showData = false
          this.params.area_id = data[data.length - 1]
          this.formData.areaId = data[data.length - 1]
          // this.formData.areaId = data.join(',')
          console.log(this.formData)
        }, 200)
      }
    },
    changeAreas (data) {
      if (data && data.length > 0) {
        this.formData.areaId = data[data.length - 1]
      } else {
        this.formData.areaId = null // 或空数组 []
      }
    },
    getDeviceType (data) {
      console.log(data, '设备类型')
    },

    on_refresh_table () {
      this.$refs.grid.query_grid_data(1)
    },
    temEvent() {
      this.$store.dispatch('authGetRequestBlob', {
        url: '/acp-com/acp/pm/baseDevice/exportModel',
        //  responseType: 'blob'
      }).then(data => {
        let blobURL = URL.createObjectURL(data);
        let link = document.createElement("a");
        link.style.display = "none";
        link.href = blobURL;
        // if (name) {
        //   link.setAttribute("download", '导出模板.xlsx');
        // }
        link.setAttribute("download", '设备管理列表模板.xlsx');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(blobURL);
      })
    },
    triggerFileInput() {
      this.$refs.fileInput.click();
    },
    handleFileChange(event) {
      const file = event.target.files[0];
      if (!file) return;

      // 验证文件类型
      const allowedExtensions = ['xls', 'xlsx'];
      const extension = file.name.split('.').pop().toLowerCase();
      if (!allowedExtensions.includes(extension)) {
        this.$message.error('只能上传 .xls 或 .xlsx 文件');
        return;
      }

      this.uploadFile(file);
    },

    async uploadFile(file) {
      const formData = new FormData();
      formData.append('file', file);

      try {
        const res = await axios.post(
          '/acp-com/acp/pm/baseDevice/importDeviceData',
          formData,
          {
            headers: {
              'Authorization': `Bearer ${getToken()}`,
            },
          }
        );
        console.log(res, 'res1111111111111111111');
        this.on_refresh_table()
        this.$Message.success(res.data.data)
      } catch (error) {
        console.error('上传失败:', error);
        this.$Message.error('文件上传失败');
      } finally {
        this.$refs.fileInput.value = '';
      }
    },
    resetGird() {
      console.log('重置')
      this.params = {}
    }
  },
  created () {
    this.getList()
  }
}
</script>

  <style lang="less" scoped>
  .ivu-btn:nth-of-type(n+1){
    margin-left: 10px;
  }
  .dbxx-wrap-search{
    background: rgba(247, 249, 252, .9);
    padding: 10px 15px;
    /* margin-bottom: 10px; */
    overflow: hidden;
    border: 1px solid #cee0f0;
    margin: 16px 10px;
  }
  .pageWrap{
    display: flex;
    justify-content: flex-end;
    padding: 0 16px 6px 0;
    margin-top: 6px;
    // border-top: 1px solid #f1f9fa;
    width: 100%;
  }
  .addBtn{
    margin-bottom: 10px;
  }
  .wrap-cont{
    // display: flex;
  }
  .tips{
    // display: flex;
    // flex-direction: column;
  }
  /deep/.ivu-modal-header-inner{
    background: none !important;
    color: #444 !important
  }
  /deep/.ivu-modal-body{
    height: 500px !important;
    max-height: 80vh !important;
    overflow-y: auto !important;
  }
  .title{
    position: relative;
    font-size: 16px;
    color: #000;
    // font-weight: 600;
    line-height: 40px;
    height: 40px;
    padding-left: 15px;
    margin-left: 10px;
  }
  .title::before {
    position: absolute;
    content: "";
    background-color: #087eff;
    width: 4px;
    height: 20px;
    left: 0;
    top: 50%;
    margin-top: -10px;
    // -webkit-border-radius: 3px;
    // -moz-border-radius: 3px;
    // border-radius: 3px;
  }
  .djxx-title{
    position: relative;
    font-size: 16px;
    color: #000;
    // font-weight: 600;
    line-height: 50px;
    padding-left: 10px;
    // margin-left: 10px;
  }
  .djxx-title::before{
    position: absolute;
    content: "";
    background-color: #087eff;
    width: 4px;
    height: 20px;
    left: 0;
    top: 50%;
    margin-top: -10px;
  }
.djxx-wrap{
  // margin:20px 10%;
  // border:1px solid #eff6ff;
  .ivu-col{
    border-right:1px solid #eff6ff;
    border-bottom:1px solid #eff6ff;
    line-height: 42px;
  }
  .ivu-col:nth-of-type(2n+1){
    font-weight: 700;
    text-align: right;
    padding-right: 16px;
    background: #e4eefc;
    // border-right: 2px solid #fff;
    border: 2px solid #fff;
    border-bottom: none;
  }
  .ivu-col:nth-of-type(2n){
    padding-left: 16px;
    background: #f5f7fa;
    height: 100%;
    border-top: 2px solid #fff;
    // border-bottom: 2px solid #fff;
    // border-left: none;
  }
}
// /deep/.ivu-btn-primary{
//   background: none !important;
// }
.input-container {
  position: relative;
  display: inline-block;
}
.unit {
  position: absolute;
  right: 1px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  color: #666;
  text-align: center;
  width: 25px;
  height: 90%;
  border-radius: 0px 4px 4px 0px;
  background: #e6f0ff;
}
/deep/.el-input__inner{
  height: 32px !important;
  line-height: 32px !important;
}
// .areaClassName /deep/.el-cascader-panel .el-scrollbar .el-cascader-menu__list{
//   min-height: 160px !important;
// }
// .areaClassName /deep/.el-cascader-panel .el-scrollbar .el-scrollbar__wrap{
//   min-height: 160px !important;
//   height: 160px !important;
// }
.areaClassName /deep/.el-radio__original {
  display: none !important;
}
/deep/.el-input.is-disabled .el-input__inner{
  border: none !important;
  color: #5A516E !important;
  padding-left: 0px !important;
}
.djxx-wrap /deep/.el-input__suffix{
  display: none;
}

  /* 摄像机选择相关样式 */
  .camera-selector {
    .selected-cameras {
      max-height: 120px;
      overflow-y: auto;
      margin-bottom: 8px;

      .ivu-tag {
        margin-right: 6px;
        margin-bottom: 6px;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .camera-tree-container {
      padding: 8px;

      .tree-header {
        margin-bottom: 8px;
        font-weight: bold;
        color: #666;
        font-size: 13px;
      }

      .el-tree {
        max-height: 350px;
        overflow-y: auto;
        border: 1px solid #e6e6e6;
        border-radius: 4px;
        padding: 8px;
        background: #fafafa;
      }
    }
  }
  </style>

<style>
.areaClassName{
    /* background: #123493;
    border: 1px solid rgba(57,106,254,1); */
}
.areaClassName .el-scrollbar__wrap{
  height: 200px !important;
}
/* .areaClassName[x-placement^=bottom] .popper__arrow {
    display: none;
}
.areaClassName .el-cascader-menu {
    color: #fff;
    border-right: 1px solid rgba(57,106,254,1);
}
.areaClassName .el-cascader-node.in-active-path {
    color: #38B4C1;
}
.areaClassName .el-cascader-node.is-active {
    color: #38B4C1;
}
.areaClassName .el-cascader-node:not(.is-disabled):focus,
.areaClassName .el-cascader-node:not(.is-disabled):hover {
    background-color: #0C0F56!important;
} */
</style>
