<template>
  <div>
    <!-- 使用示例 -->
    <div class="info-display">
      <div class="info-row">
        <span class="label">姓名：</span>
        <span class="value">{{ formatValue(userData.name) }}</span>
      </div>
      <div class="info-row">
        <span class="label">年龄：</span>
        <span class="value">{{ formatValue(userData.age) }}</span>
      </div>
      <div class="info-row">
        <span class="label">电话：</span>
        <span class="value">{{ formatValue(userData.phone, '未填写') }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { formatValue } from '@/util'

export default {
  name: 'FormatValueExample',
  data() {
    return {
      userData: {
        name: null,
        age: '',
        phone: 'null'
      }
    }
  },
  methods: {
    // 方式一：在methods中引入
    formatValue,
    
    // 方式二：也可以创建自己的包装方法
    displayValue(value, defaultText = '暂无') {
      return formatValue(value, defaultText)
    }
  }
}
</script>

<style scoped>
.info-display {
  padding: 20px;
}

.info-row {
  margin-bottom: 10px;
}

.label {
  font-weight: bold;
  margin-right: 10px;
}

.value {
  color: #666;
}
</style>
